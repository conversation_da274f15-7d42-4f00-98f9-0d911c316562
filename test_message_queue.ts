import { DBOS } from '@dbos-inc/dbos-sdk';
import { MessageQueueService } from './src/core/MessageQueueService';
import { ConversationService } from './src/core/ConversationService';

/**
 * Test script to validate the new message queue system
 */
async function testMessageQueue() {
  console.log('🧪 Testing Message Queue System...\n');

  try {
    // Create a test conversation
    console.log('1. Creating test conversation...');
    const conversation = await ConversationService.createConversation();
    console.log(`✅ Created conversation ${conversation.id}\n`);

    // Test 1: Basic message enqueuing
    console.log('2. Testing basic message enqueuing...');
    const message1 = await MessageQueueService.enqueueMessage({
      conversation_id: conversation.id,
      character: 'Alice',
      text: 'Hello there! How are you doing today?',
      delay_ms: 2000
    });
    
    const message2 = await MessageQueueService.enqueueMessage({
      conversation_id: conversation.id,
      character: '<PERSON>',
      text: 'Hey! I am doing great, thanks for asking!',
      delay_ms: 3000
    });

    console.log(`✅ Enqueued message from <PERSON>: ${message1?.text.substring(0, 30)}...`);
    console.log(`✅ Enqueued message from Bob: ${message2?.text.substring(0, 30)}...\n`);

    // Test 2: Duplicate detection
    console.log('3. Testing duplicate detection...');
    const duplicateMessage = await MessageQueueService.enqueueMessage({
      conversation_id: conversation.id,
      character: 'Alice',
      text: 'Hello there! How are you doing today?', // Exact duplicate
      delay_ms: 2000
    });

    if (!duplicateMessage) {
      console.log('✅ Duplicate message correctly rejected\n');
    } else {
      console.log('❌ Duplicate message was not rejected\n');
    }

    // Test 3: Similarity detection
    console.log('4. Testing similarity detection...');
    const similarMessage = await MessageQueueService.enqueueMessage({
      conversation_id: conversation.id,
      character: 'Alice',
      text: 'Hi there! How are you doing?', // Similar but not identical
      delay_ms: 2000
    });

    if (!similarMessage) {
      console.log('✅ Similar message correctly rejected\n');
    } else {
      console.log('❌ Similar message was not rejected\n');
    }

    // Test 4: Different character, similar message (should be allowed)
    console.log('5. Testing similar message from different character...');
    const differentCharacterMessage = await MessageQueueService.enqueueMessage({
      conversation_id: conversation.id,
      character: 'Charlie',
      text: 'Hello there! How are you doing today?',
      delay_ms: 2000
    });

    if (differentCharacterMessage) {
      console.log('✅ Similar message from different character correctly allowed\n');
    } else {
      console.log('❌ Similar message from different character was incorrectly rejected\n');
    }

    // Test 5: Get pending messages
    console.log('6. Testing pending message retrieval...');
    const pendingMessages = await MessageQueueService.getPendingMessages(conversation.id);
    console.log(`✅ Found ${pendingMessages.length} pending messages:`);
    for (const msg of pendingMessages) {
      console.log(`   - ${msg.character}: ${msg.text.substring(0, 40)}... (priority: ${msg.priority})`);
    }
    console.log();

    // Test 6: Queue statistics
    console.log('7. Testing queue statistics...');
    const stats = await MessageQueueService.getQueueStats(conversation.id);
    console.log('✅ Queue statistics:');
    console.log(`   - Pending: ${stats.pending}`);
    console.log(`   - Processing: ${stats.processing}`);
    console.log(`   - Sent: ${stats.sent}`);
    console.log(`   - Cancelled: ${stats.cancelled}`);
    console.log(`   - Total: ${stats.total}`);
    console.log();

    // Test 7: Message status updates
    console.log('8. Testing message status updates...');
    if (pendingMessages.length > 0) {
      const firstMessage = pendingMessages[0];
      await MessageQueueService.updateMessageStatus(firstMessage.id, 'PROCESSING');
      console.log(`✅ Updated message ${firstMessage.id} to PROCESSING status`);
      
      await MessageQueueService.updateMessageStatus(firstMessage.id, 'SENT');
      console.log(`✅ Updated message ${firstMessage.id} to SENT status`);
    }
    console.log();

    // Test 8: Ready messages (simulate scheduled time passing)
    console.log('9. Testing ready message detection...');
    // Update scheduled times to make messages ready
    if (pendingMessages.length > 1) {
      const secondMessage = pendingMessages[1];
      await DBOS.knexClient('forachat.message_queue')
        .where('id', secondMessage.id)
        .update({ scheduled_at: new Date(Date.now() - 1000) }); // 1 second ago
      
      const readyMessages = await MessageQueueService.getReadyMessages(conversation.id);
      console.log(`✅ Found ${readyMessages.length} ready messages`);
    }
    console.log();

    // Test 9: Debug information
    console.log('10. Testing debug information...');
    const debugInfo = await MessageQueueService.getQueueDebugInfo(conversation.id);
    console.log('✅ Debug information:');
    console.log(`   - Pending messages: ${debugInfo.pendingMessages.length}`);
    console.log(`   - Processing messages: ${debugInfo.processingMessages.length}`);
    console.log(`   - Recent sent messages: ${debugInfo.recentSentMessages.length}`);
    console.log();

    // Test 10: Cleanup
    console.log('11. Testing cleanup...');
    const cleanedUp = await MessageQueueService.cleanupOldMessages();
    console.log(`✅ Cleaned up ${cleanedUp} old messages\n`);

    console.log('🎉 All tests completed successfully!');
    console.log('\n📊 Final Statistics:');
    const finalStats = await MessageQueueService.getQueueStats(conversation.id);
    console.log(`   - Pending: ${finalStats.pending}`);
    console.log(`   - Processing: ${finalStats.processing}`);
    console.log(`   - Sent: ${finalStats.sent}`);
    console.log(`   - Cancelled: ${finalStats.cancelled}`);
    console.log(`   - Total: ${finalStats.total}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  DBOS.launch().then(() => {
    testMessageQueue().then(() => {
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    }).catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
  });
}

export { testMessageQueue };
