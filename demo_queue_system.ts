import { DBOS } from '@dbos-inc/dbos-sdk';
import { MessageQueueService } from './src/core/MessageQueueService';
import { ConversationService } from './src/core/ConversationService';

/**
 * Demo script showing the message queue system preventing duplicates and similar messages
 */
async function demoQueueSystem() {
  console.log('🚀 Message Queue System Demo\n');
  console.log('This demo shows how the new queue system prevents duplicate and similar messages\n');

  try {
    // Create a test conversation
    const conversation = await ConversationService.createConversation();
    console.log(`📝 Created conversation ${conversation.id}\n`);

    // Simulate multiple characters trying to send similar greetings
    console.log('🎭 Simulating multiple characters sending similar greetings...\n');

    const greetingAttempts = [
      { character: 'Alice', text: 'Hey bestie! How are you doing today?', delay: 2000 },
      { character: 'Bob', text: 'Hey bestie! What\'s up?', delay: 2500 },
      { character: '<PERSON>', text: 'Hey bestie! Hope you\'re having a great day!', delay: 3000 },
      { character: '<PERSON>', text: 'Hey bestie! How are you doing today?', delay: 2000 }, // Exact duplicate
      { character: '<PERSON>', text: 'Hello there! How are things going?', delay: 2200 }, // Different enough
      { character: 'Bob', text: 'Hey bestie! How are you?', delay: 2800 }, // Similar to Bob's first
      { character: 'Eve', text: 'Good morning! Hope you slept well!', delay: 3500 }, // Different topic
    ];

    let acceptedCount = 0;
    let rejectedCount = 0;

    for (const attempt of greetingAttempts) {
      console.log(`Attempting to enqueue: ${attempt.character} - "${attempt.text}"`);
      
      const result = await MessageQueueService.enqueueMessage({
        conversation_id: conversation.id,
        character: attempt.character,
        text: attempt.text,
        delay_ms: attempt.delay
      });

      if (result) {
        console.log(`  ✅ ACCEPTED (ID: ${result.id}, Priority: ${result.priority})`);
        acceptedCount++;
      } else {
        console.log(`  ❌ REJECTED (duplicate/similar)`);
        rejectedCount++;
      }
      console.log();
    }

    console.log(`📊 Results: ${acceptedCount} accepted, ${rejectedCount} rejected\n`);

    // Show the final queue state
    console.log('📋 Final Queue State:\n');
    const pendingMessages = await MessageQueueService.getPendingMessages(conversation.id);
    
    console.log('Pending Messages (in priority order):');
    for (let i = 0; i < pendingMessages.length; i++) {
      const msg = pendingMessages[i];
      console.log(`  ${i + 1}. ${msg.character}: "${msg.text}"`);
      console.log(`     Priority: ${msg.priority}, Delay: ${msg.delay_ms}ms, Scheduled: ${new Date(msg.scheduled_at!).toLocaleTimeString()}`);
    }
    console.log();

    // Show queue statistics
    const stats = await MessageQueueService.getQueueStats(conversation.id);
    console.log('📈 Queue Statistics:');
    console.log(`  - Total messages: ${stats.total}`);
    console.log(`  - Pending: ${stats.pending}`);
    console.log(`  - Processing: ${stats.processing}`);
    console.log(`  - Sent: ${stats.sent}`);
    console.log(`  - Cancelled: ${stats.cancelled}`);
    
    if (stats.oldestPending) {
      console.log(`  - Oldest pending: ${stats.oldestPending.toLocaleString()}`);
    }
    if (stats.newestPending) {
      console.log(`  - Newest pending: ${stats.newestPending.toLocaleString()}`);
    }
    console.log();

    // Simulate processing some messages
    console.log('⚡ Simulating message processing...\n');
    
    const readyMessages = await MessageQueueService.getReadyMessages(conversation.id);
    console.log(`Found ${readyMessages.length} messages ready to be sent immediately`);
    
    // Make some messages ready by updating their scheduled time
    if (pendingMessages.length > 0) {
      const messagesToMakeReady = pendingMessages.slice(0, Math.min(3, pendingMessages.length));
      
      for (const msg of messagesToMakeReady) {
        await DBOS.knexClient('forachat.message_queue')
          .where('id', msg.id)
          .update({ scheduled_at: new Date(Date.now() - 1000) }); // 1 second ago
      }
      
      const nowReadyMessages = await MessageQueueService.getReadyMessages(conversation.id);
      console.log(`\nAfter updating scheduled times: ${nowReadyMessages.length} messages are now ready`);
      
      // Process them
      for (const msg of nowReadyMessages) {
        console.log(`Processing: ${msg.character} - "${msg.text.substring(0, 40)}..."`);
        await MessageQueueService.updateMessageStatus(msg.id, 'PROCESSING');
        
        // Simulate some processing time
        await new Promise(resolve => setTimeout(resolve, 100));
        
        await MessageQueueService.updateMessageStatus(msg.id, 'SENT');
        console.log(`  ✅ Sent successfully`);
      }
    }
    console.log();

    // Final statistics
    const finalStats = await MessageQueueService.getQueueStats(conversation.id);
    console.log('🏁 Final Statistics:');
    console.log(`  - Total messages processed: ${finalStats.total}`);
    console.log(`  - Successfully sent: ${finalStats.sent}`);
    console.log(`  - Still pending: ${finalStats.pending}`);
    console.log(`  - Processing: ${finalStats.processing}`);
    console.log(`  - Cancelled: ${finalStats.cancelled}`);
    
    console.log('\n🎉 Demo completed! The queue system successfully:');
    console.log('  ✅ Prevented duplicate messages');
    console.log('  ✅ Detected and filtered similar messages');
    console.log('  ✅ Maintained proper message ordering');
    console.log('  ✅ Tracked message status throughout the lifecycle');
    console.log('  ✅ Provided comprehensive monitoring and debugging info');

  } catch (error) {
    console.error('❌ Demo failed:', error);
    throw error;
  }
}

// Run the demo if this file is executed directly
if (require.main === module) {
  DBOS.launch().then(() => {
    demoQueueSystem().then(() => {
      console.log('\n✅ Demo completed successfully');
      process.exit(0);
    }).catch((error) => {
      console.error('\n❌ Demo failed:', error);
      process.exit(1);
    });
  });
}

export { demoQueueSystem };
